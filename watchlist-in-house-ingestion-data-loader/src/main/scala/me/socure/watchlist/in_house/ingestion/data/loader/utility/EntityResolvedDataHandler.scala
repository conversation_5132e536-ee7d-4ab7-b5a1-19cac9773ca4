package me.socure.watchlist.in_house.ingestion.data.loader.utility

import me.socure.watchlist.in_house.ingestion.common.models.Constants.{Key => _, _}
import me.socure.watchlist.in_house.ingestion.common.models._
import me.socure.watchlist.in_house.ingestion.common.utility.WatchlistInHouseIngestionTransliterator
/**
 * Created by <PERSON><PERSON> on Sep 21, 2023
 */
object EntityResolvedDataHandler {

  def convertEntitiesToESCompatibleRecords(entitiesMap: Map[Key, ResolvedEntityRecordDetails], sourceId: Int, runId: String): Map[Key, ESRecord] = {
    entitiesMap.foldLeft(Map.empty[Key, ESRecord])((resultMap, entity) => {
      val entityIdSourceIdkey = entity._1
      val resolvedEntityRecords = entity._2.resolvedEntityRecords
      val entityOperation = entity._2.entityOperation
      val otherMap = resolvedEntityRecords.map(_.JSON_DATA.OTHER).map(data => JSONUtil.constructOthersMap(data))
      val entityNamePlain = checkIfEmpty(getEntityName(resolvedEntityRecords))
      val akaNamePlain = checkIfEmpty(getAka(resolvedEntityRecords))

      // Check for OpenSanctions-specific sourceId and vendorSourceId in OTHER field
      val (finalSourceId, finalVendorSourceId) = extractOpenSanctionsSourceIds(otherMap, entityIdSourceIdkey.sourceId.toInt)

      resultMap ++ Map(entityIdSourceIdkey -> ESRecord(Record(
        meta = Meta(
          entityId = entityIdSourceIdkey.entityId,
          sourceId = Set(finalSourceId),
          vendorSourceId = Set(finalVendorSourceId),
          runId = runId,
          entityType = checkIfEmpty(getEntityType(resolvedEntityRecords))
        ),
        names = NameRecord(
          entity = EntityName(
            plain = entityNamePlain,
            initials = checkIfEmpty(getEntityNameInitial(resolvedEntityRecords)),
            transform = entityNamePlain.map(_.flatMap(WatchlistInHouseIngestionTransliterator.transliterate))
          ),
          aka = AkaName(
            plain = akaNamePlain,
            initials = checkIfEmpty(getAkaInitial(resolvedEntityRecords)),
            transform = akaNamePlain.map(_.flatMap(WatchlistInHouseIngestionTransliterator.transliterate))
          )
        ),
        dates = Date(
          yearOfBirth = checkIfEmptyInt(getYearOfBirth(resolvedEntityRecords)),
          dateOfBirth = checkIfEmptyInt(getDataOfBirth(resolvedEntityRecords))
        ),
        locations = Location(
          address = checkIfEmpty(getAddress(resolvedEntityRecords)),
          country = checkIfEmpty(getCountry(resolvedEntityRecords)),
          city = checkIfEmpty(getCity(resolvedEntityRecords)),
          state = checkIfEmpty(getState(resolvedEntityRecords))
        ),
        contacts = Contacts(
          phone = Set.empty[String],
          email = Set.empty[String]
        ),
        ids = Id(
          nationalId = checkIfEmpty(getNationalId(resolvedEntityRecords)),
          passport = checkIfEmpty(getPassport(resolvedEntityRecords)),
          drivingLicense = checkIfEmpty(getDrivingLicense(resolvedEntityRecords)),
          cryptoWalletId = checkIfEmpty(getCryptoWalletId(resolvedEntityRecords))
        )
      ),entityOperation = entityOperation))
    })
  }

  private def checkIfEmpty(inputSet: Set[String]): Option[Set[String]] = {
    if (inputSet.forall(_.equals(EMPTY_STRING))) None else Some(inputSet.filterNot(_.equals(EMPTY_STRING)))
  }

  private def checkIfEmptyInt(inputSet: Set[Int]): Option[Set[Int]] = {
    if (inputSet.isEmpty) None else Some(inputSet)
  }

  private def getEntityName(records: Set[ResolvedEntityRecord]): Set[String] = {
    val individualRecords = records.map(record => record.JSON_DATA.NAME_LIST.filter(entry => entry.NAME_TYPE.equals(INDIVIDUAL_NAME)).map(_.NAME_FULL.get).mkString(COMMA_SEPARATOR))
    val orgRecords = records.map(record => record.JSON_DATA.NAME_LIST.filter(entry => entry.NAME_TYPE.equals(ORGANISATION_NAME)).map(_.NAME_ORG.get).mkString(COMMA_SEPARATOR))
    individualRecords.filterNot(_.equals(EMPTY_STRING)) ++ orgRecords.filterNot(_.equals(EMPTY_STRING))
  }

  private def getEntityNameInitial(records: Set[ResolvedEntityRecord]): Set[String] = {
    val entityNameInitials = records.flatMap(record => record.JSON_DATA.NAME_LIST.filter(entry => entry.NAME_TYPE.equals(INDIVIDUAL_NAME)).map(_.NAME_FULL.get))
    val orgNameInitials = records.flatMap(record => record.JSON_DATA.NAME_LIST.filter(entry => entry.NAME_TYPE.equals(ORGANISATION_NAME)).map(_.NAME_ORG.get))
    entityNameInitials.flatMap {
      entityNameInitial => ESDataStringProcessor.processEntityNameInitial((entityNameInitial ++ orgNameInitials).mkString)
    }
  }

   private def getAka(records: Set[ResolvedEntityRecord]): Set[String] = {
    records.flatMap(record => record.JSON_DATA.NAME_LIST.filter(entry => entry.NAME_TYPE.equals(AKA)).map(_.NAME_FULL.get))
  }

  private def getAkaInitial(records: Set[ResolvedEntityRecord]): Set[String] = {
    val akaInitials = records.flatMap(record => record.JSON_DATA.NAME_LIST.filter(entry => entry.NAME_TYPE.equals(AKA)).map(_.NAME_FULL.get))
    akaInitials.flatMap {
      akaInitial => ESDataStringProcessor.processAkaInitial(akaInitial)
    }
  }

  def getEntityType(records: Set[ResolvedEntityRecord]): Set[String] = {
    records.map(record => if (record.JSON_DATA.SOURCE_ENTITY_TYPE.exists(_.nonEmpty)) {
      record.JSON_DATA.SOURCE_ENTITY_TYPE.get
    } else {
     DEFAULT_ENTITY_TYPE
    })
  }

  private def getYearOfBirth(records: Set[ResolvedEntityRecord]): Set[Int] = {
    getDataOfBirth(records).map(_.toString.substring(0,4).toInt)
  }

  private def getDataOfBirth(records: Set[ResolvedEntityRecord]): Set[Int] = {
    // mm/dd/yyyy to yyyymmdd
   val datesString = records.map(record => record.JSON_DATA.DATE_OF_BIRTH_LIST match {
     case Some(list) => list.map(_.DATE_OF_BIRTH)
     case None => List.empty[String]
   }
   )
    val filteredDates = datesString.map(_.filterNot(_ == "0000-00-00")).filter(_.nonEmpty)

    filteredDates.flatten.map {
      date =>
        DateUtil.formatdate(date)
    }
  }

  private def getAddress(records: Set[ResolvedEntityRecord]): Set[String] = {
    records.flatMap(_.JSON_DATA.ADDRESS_LIST.flatMap(_.ADDR_FULL))
      .filter(_.nonEmpty)
  }

  private def getCountry(records: Set[ResolvedEntityRecord]): Set[String] = {
    records.flatMap(_.JSON_DATA.COUNTRY_LIST.flatMap(_.CITIZENSHIP))
      .filter(_.nonEmpty)
  }

  private def getCity(records: Set[ResolvedEntityRecord]): Set[String] = {
    records.map(record => record.JSON_DATA.CITY.getOrElse(EMPTY_STRING))
  }

  private def getState(records: Set[ResolvedEntityRecord]): Set[String] = {
    records.map(record => record.JSON_DATA.STATE.getOrElse(EMPTY_STRING))
  }

  private def getNationalId(records: Set[ResolvedEntityRecord]): Set[String] = {
    records.map(record => record.JSON_DATA.NATIONAL_ID.getOrElse(EMPTY_STRING))
  }

  private def getPassport(records: Set[ResolvedEntityRecord]): Set[String] = {
    records.map(record => record.JSON_DATA.PASSPORT.getOrElse(EMPTY_STRING))
  }

  private def getDrivingLicense(records: Set[ResolvedEntityRecord]): Set[String] = {
    records.map(record => record.JSON_DATA.DRIVING_LICENSE.getOrElse(EMPTY_STRING))
  }

  private def getCryptoWalletId(records: Set[ResolvedEntityRecord]): Set[String] = {
    records.map(record => record.JSON_DATA.CRYPTO_WALLET_ID.getOrElse(EMPTY_STRING))
  }


  def convertDynamoDBToESRecords(dynamoDBDetailsList: List[DynamoDBEntityDetails], tableName: String): Map[String, ESRecord] = {
    val esRecordList = dynamoDBDetailsList.map {  dynamoDBEntity =>
      val entityId = dynamoDBEntity.entityId

      val sourceIdSet = Set(dynamoDBEntity.sourceId)

      val meta = Meta(
        entityId = entityId,
        sourceId = sourceIdSet,
        runId = dynamoDBEntity.runId,
        entityType = dynamoDBEntity.entityType,
        dynamoTableReference = Option(tableName),
        vendorSourceId = dynamoDBEntity.vendorSourceId
      )

      val names = NameRecord(
        entity = EntityName(
          plain = dynamoDBEntity.entityName,
          initials = dynamoDBEntity.entityName.map { names =>
            names.flatMap { name =>
              ESDataStringProcessor.processEntityNameInitial(name.mkString)
            }.filter(_.nonEmpty)
          },
          transform = dynamoDBEntity.entityNameTransforms
        ),
        aka = AkaName(
          plain = dynamoDBEntity.aka,
          initials = dynamoDBEntity.aka.map { akas =>
            akas.flatMap { aka =>
              ESDataStringProcessor.processAkaInitial(aka.mkString)
            }.filter(_.nonEmpty)
          },
          transform = dynamoDBEntity.akaTransforms
        )
      )

      val dates = Date(
        yearOfBirth = dynamoDBEntity.yearOfBirth,
        dateOfBirth = dynamoDBEntity.dateOfBirth
      )

      val locations = Location(
        address = dynamoDBEntity.address,
        country = dynamoDBEntity.country,
        city = dynamoDBEntity.city,
        state = dynamoDBEntity.state
      )

      val ids = Id(
        nationalId = dynamoDBEntity.nationalId,
        passport = dynamoDBEntity.passport,
        drivingLicense = dynamoDBEntity.drivingLicense,
        cryptoWalletId = dynamoDBEntity.cryptoWalletId
      )
      val contacts = Contacts(
        email = dynamoDBEntity.email.getOrElse(Set.empty[String]),
        phone = dynamoDBEntity.phone.getOrElse(Set.empty[String])
      )

      val record = Record(meta, names, dates, locations,contacts,  ids)
       ESRecord(record, ENTITY_OPERATION_ADD.toInt)

    }
    val groupedESRecords: Map[String, List[ESRecord]] = esRecordList.groupBy(_.record.meta.entityId)

    val mergedESRecords: Map[String, ESRecord] = groupedESRecords.map { case (entityId, esRecords) =>
      val mergedRecord = esRecords.reduce(_ ++ _)
      entityId -> mergedRecord
    }

    mergedESRecords
  }
}
