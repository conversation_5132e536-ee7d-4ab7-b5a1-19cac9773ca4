package me.socure.watchlist.in_house.ingestion.common.models.enums

import me.socure.types.scala.{ByMember, Enum}
import me.socure.watchlist.in_house.ingestion.common.models.Constants.{ACURIS, BUSINESS, INDIVIDUALS, VENDOR}

object WatchlistSources extends Enum with ByMember {
  type WatchlistSource = EnumVal

  case class EnumVal(id: Int,
                     name: String,
                     category: String,
                     listType: String,
                     location: String,
                     webScraperScriptName: String,
                     isActive: Boolean = true,
                     isManualUpdateEnabled: Boolean = false,
                     fileType: String = "csv",
                     updateFrequencyInMinutes: Int = 15,
                     workflow: String = "standard",
                     enrichmentScriptName: Option[String] = None
                    ) extends Value

//  val UN_CONSOLIDATED = new WatchlistSource(id = 1, name = "United Nations Consolidated", category = "Sanctions", listType = "Sanctions", location = "International", webScraperScriptName = "un_consolidated", isActive = true, isManualUpdateEnabled = false)
//  val EUROPE_SANCTIONS_LIST = new WatchlistSource(id = 2, name = "EU External Action Service - Consolidated list of Sanctions", category = "Sanctions", listType = "Sanctions", location = "International", webScraperScriptName = "europe_sanctions_list", isActive = true, isManualUpdateEnabled = false)
//  val UK_HM_TREASURY_FINANCIAL_SANCTIONS = new WatchlistSource(id = 3, name = "United Kingdom HM Treasury Office of Financial Sanctions Implementation Consolidated List", category = "Sanctions", listType = "Sanctions", location = "United Kingdom", webScraperScriptName = "uk_hm_treasury_financial_sanctions", isActive = true, isManualUpdateEnabled = false)
//  val CA_CONSOLIDATED_SANCTIONS = new WatchlistSource(id = 4, name = "Consolidated Canadian Autonomous Sanctions List", category = "Sanctions", listType = "Sanctions", location = "Canada", webScraperScriptName = "ca_consolidated_sanctions", isActive = true, isManualUpdateEnabled = false)
//  val OFAC_NON_SDN_LIST = new WatchlistSource(id = 5, name = "OFAC Consolidated List", category = "Sanctions", listType = "Sanctions", location = "United States", webScraperScriptName = "ofac_non_sdn_list", updateFrequencyInMinutes= 120, isActive = true, isManualUpdateEnabled = false)
//  val OFAC_SDN_LIST = new WatchlistSource(id = 6, name = "OFAC SDN List", category = "Sanctions", listType = "Sanctions", location = "United States", webScraperScriptName = "ofac_sdn_list", updateFrequencyInMinutes= 120, isActive = true, isManualUpdateEnabled = false)
//  val US_BUREAU_OF_INDUSTRY_AND_SECURITY_ENTITY_LIST = new WatchlistSource(id = 7, name = "United States Bureau of Industry and Security Entity List", category = "Sanctions", listType = "Sanctions", location = "United States", webScraperScriptName = "us_bureau_of_industry_and_security_entity_list", isActive = true, isManualUpdateEnabled = false)
//  val US_BUREAU_OF_INDUSTRY_AND_SECURITY_DENIED_PERSONS_LIST = new WatchlistSource(id = 8, name = "United States Bureau of Industry and Security The Denied Persons List", category = "Sanctions", listType = "Sanctions", location = "United States", webScraperScriptName = "us_bureau_of_industry_and_security_denied_person_list", isActive = true, isManualUpdateEnabled = false)
//  val US_DEPT_STATE_NONPROLIFERATION_SANCTIONS = new WatchlistSource(id = 9, name = "United States Department of State Nonproliferation Sanctions List", category = "Sanctions", listType = "Sanctions", location = "United States", webScraperScriptName = "us_dept_state_nonproliferation_sanctions", isActive = true, isManualUpdateEnabled = false)
//  val US_DEPT_STATE_TERRORIST_EXCLUSION_LIST = new WatchlistSource(id = 10, name = "United States Department of State Terror Exclusion List", category = "Sanctions", listType = "Sanctions", location = "United States", webScraperScriptName = "us_dept_state_terrorist_exclusion_list", isActive = true, isManualUpdateEnabled = false)
//  val US_TREASURY_FINCEN_EA = new WatchlistSource(id = 11, name = "United States Treasury FinCEN Enforcement Actions", category = "Enforcement", listType = "Warning", location = "United States", webScraperScriptName = "us_treasury_fincen_ea", isActive = true, isManualUpdateEnabled = false)
//  val US_OCC_ENFORCEMENT_ACTION = new WatchlistSource(id = 12, name = "United States OCC Enforcements", category = "Enforcement", listType = "Warning", location = "United States", webScraperScriptName = "us_occ_enforcement_action", isActive = true, isManualUpdateEnabled = false)
//  val US_DEA_FUGITIVES = new WatchlistSource(id = 13, name = "United States Drug Enforcement Agency Fugitives", category = "Enforcement", listType = "Warning", location = "United States", webScraperScriptName = "us_dea_fugitives", isActive = true, isManualUpdateEnabled = false, fileType="parquet", updateFrequencyInMinutes = 60, workflow = "standalone")
//  val US_ICE_WANTED = new WatchlistSource(id = 14, name = "United States Immigration and Customs Wanted", category = "Enforcement", listType = "Warning", location = "United States", webScraperScriptName = "us_ice_wanted", isActive = true, isManualUpdateEnabled = false, fileType="parquet", updateFrequencyInMinutes = 15)
//  val US_MARSHAL_PROFILED_FUGITIVES = new WatchlistSource(id = 15, name = "United States Marshals Wanted", category = "Enforcement", listType = "Warning", location = "United States", webScraperScriptName = "us_marshal_profiled_fugitives", isActive = true, isManualUpdateEnabled = false)
//  val FBI_WANTED_LIST = new WatchlistSource(id = 16, name = "FBI Most Wanted", category = "Enforcement", listType = "Warning", location = "United States", webScraperScriptName = "fbi_wanted_list" , isActive = true, isManualUpdateEnabled = false)
//  val US_FED_PROHIBITION_LIST = new WatchlistSource(id = 17, name = "United States Federal Reserve Prohibition from Banking", category = "Enforcement", listType = "Warning", location = "United States", webScraperScriptName = "us_fed_prohibition_list", isActive = true, isManualUpdateEnabled = false)
//  val MEDICAID_EXCLUSIONS_LISTS_OIG_LEIE = new WatchlistSource(id = 18, name = "Fitness Probity United States Medicaid Exclusions Lists OIG LEIE", category = "Enforcement", listType = "Fitness Probity", location = "United States", webScraperScriptName = "medicaid_exclusions_lists_oig_leie", isActive = true, isManualUpdateEnabled = false)
//  val US_DEPARTMENT_OF_JUSTICE_IRDP = new WatchlistSource(id = 19, name = "United States Department of Justice Executive Office for Immigration Review Disciplined Practitioners", category = "Enforcement", listType = "Fitness Probity", location = "United States", webScraperScriptName = "us_department_of_justice_irdp", isActive = true, isManualUpdateEnabled = false, updateFrequencyInMinutes = 15)
//  val US_SAM_EXCLUSIONS = new WatchlistSource(id = 20, name = "United States System for Award Management Exclusions excl OFAC", category = "Enforcement", listType = "Fitness Probity", location = "United States", webScraperScriptName = "us_sam_exclusions", isActive = true, isManualUpdateEnabled = false, updateFrequencyInMinutes = 120, workflow = "standalone")
//  val US_FINANCIAL_CRIME_ENFORCEMENT_311 = new WatchlistSource(id = 21, name = "United States Financial Crimes Enforcement Network 311 Special Measures", category = "Sanctions", listType = "Sanctions", location = "United States", webScraperScriptName = "us_financial_crime_enforcement_311", isActive = true, isManualUpdateEnabled = false)
  val ACURIS_INDIVIDUALS_DELTA = new WatchlistSource(id = 22, name = ACURIS, category = VENDOR, listType = "na", location = "all", webScraperScriptName = INDIVIDUALS)
  val ACURIS_BUSINESS_DELTA = new WatchlistSource(id = 23, name = ACURIS, category = VENDOR, listType = "na", location = "all", webScraperScriptName = BUSINESS)


//  val Canadian_Freezing_Assets_of_Corrupt_Foreign_Officials_Act = new WatchlistSource(id = 7284,  name = "Canadian Freezing Assets of Corrupt Foreign Officials Act",  category = "Sanctions",  listType = "Sanctions", location = "International",  webScraperScriptName = "ca_facfoa",  updateFrequencyInMinutes = 50000, isActive = true, isManualUpdateEnabled = false, workflow = "opensanctions")
//  val UN_Consolidated_Related_Entities = new WatchlistSource(id = 7300,  name = "UN Consolidated - Related Entities",  category = "Sanctions",  listType = "Sanctions", location = "International",  webScraperScriptName = "us_ofac_cons",  updateFrequencyInMinutes = 10, isActive = true, isManualUpdateEnabled = false, workflow = "opensanctions")
//  val Iran_March_and_May_2017_Sanctions_against_US_entities = new WatchlistSource(id = 7302,  name = "Iran March and May 2017 Sanctions against US entities",  category = "Sanctions",  listType = "Sanctions", location = "International",  webScraperScriptName = "ir_sanctions",  updateFrequencyInMinutes = 50000, isActive = true, isManualUpdateEnabled = false, workflow = "opensanctions")
//  val United_States_Department_of_State_Cuban_Restricted_Entities_List = new WatchlistSource(id = 7365,  name = "United States Department of State Cuban Restricted Entities List",  category = "Sanctions",  listType = "Sanctions", location = "International",  webScraperScriptName = "us_cuba_sanctions",  updateFrequencyInMinutes = 30, isActive = true, isManualUpdateEnabled = false, workflow = "opensanctions")
//  val OFAC_SDN_List_Related_Entities = new WatchlistSource(id = 7376,  name = "OFAC SDN List - Related Entities",  category = "Sanctions",  listType = "Sanctions", location = "International",  webScraperScriptName = "us_ofac_sdn",  updateFrequencyInMinutes = 10, isActive = true, isManualUpdateEnabled = false, workflow = "opensanctions")
//  val Interpol_Wanted = new WatchlistSource(id = 7619,  name = "Interpol Wanted",  category = "Sanctions",  listType = "Sanctions", location = "International",  webScraperScriptName = "interpol_red_notices",  updateFrequencyInMinutes = 10, isActive = true, isManualUpdateEnabled = false, workflow = "opensanctions")
//  val United_States_Department_of_State_Directorate_of_Defense_Trade_Controls_AECA_and_ITAR_Compliance_Actions = new WatchlistSource(id = 7928,  name = "United States Department of State Directorate of Defense Trade Controls AECA and ITAR Compliance Actions",  category = "Sanctions",  listType = "Sanctions", location = "International",  webScraperScriptName = "us_ddtc_debarred",  updateFrequencyInMinutes = 15, isActive = true, isManualUpdateEnabled = false, workflow = "opensanctions")
//  val United_States_FDIC_Enforcements = new WatchlistSource(id = 7932,  name = "United States FDIC Enforcements",  category = "Sanctions",  listType = "Sanctions", location = "International",  webScraperScriptName = "us_fdic_failed_banks",  updateFrequencyInMinutes = 15, isActive = true, isManualUpdateEnabled = false, workflow = "opensanctions")
//  val United_Nations_Security_Council_List_of_Designated_Vessels = new WatchlistSource(id = 8406,  name = "United Nations Security Council List of Designated Vessels",  category = "Sanctions",  listType = "Sanctions", location = "International",  webScraperScriptName = "un_1718_vessels",  updateFrequencyInMinutes = 30, isActive = true, isManualUpdateEnabled = false, workflow = "opensanctions")
//  val World_Bank_Debarred_Firm_List = new WatchlistSource(id = 8443,  name = "World Bank Debarred Fxirm List",  category = "Sanctions",  listType = "Sanctions", location = "International",  webScraperScriptName = "worldbank_debarred",  updateFrequencyInMinutes = 15, isActive = true, isManualUpdateEnabled = false, workflow = "opensanctions")
//  val US_TRADE_CSL = new WatchlistSource(id = 8465,  name = "UN Security Council 1718 Designated Vessels List",  category = "Sanctions",  listType = "Sanctions", location = "International",  webScraperScriptName = "us_trade_csl",  updateFrequencyInMinutes = 30, isActive = true, isManualUpdateEnabled = false, workflow = "opensanctions")

  val OPENSANCTIONS_PEP_CLASS_1 = new WatchlistSource(
    id = 8363,
    name = "Pep Class 1 (e.g. Heads of State, US Historical Congress, etc.)",
    category = "PEP",
    listType = "PEP Class 1",
    location = "International",
    webScraperScriptName = "peps",
    updateFrequencyInMinutes = 1440, // Daily
    isActive = true,
    isManualUpdateEnabled = false,
    workflow = "opensanctions"
//    enrichmentScriptName = Some("wikidata")
  )

  val OPENSANCTIONS_PEP_CLASS_2 = new WatchlistSource(
    id = 8364,
    name = "Pep Class 2 (e.g. Senior officials of the military, judiciary, etc.)",
    category = "PEP",
    listType = "PEP Class 2",
    location = "International",
    webScraperScriptName = "peps",
    updateFrequencyInMinutes = 1440, // Daily
    isActive = true,
    isManualUpdateEnabled = false,
    workflow = "opensanctions"
  )

  val OPENSANCTIONS_PEP_CLASS_3 = new WatchlistSource(
    id = 8365,
    name = "Pep Class 3 (e.g. Senior mgmt & board of State-owned enterprises, etc.)",
    category = "PEP",
    listType = "PEP Class 3",
    location = "International",
    webScraperScriptName = "peps",
    updateFrequencyInMinutes = 1440, // Daily
    isActive = true,
    isManualUpdateEnabled = false,
    workflow = "opensanctions"
  )

  val OPENSANCTIONS_PEP_CLASS_4 = new WatchlistSource(
    id = 8366,
    name = "Pep Class 4 (e.g. Mayor and local county, city and district assembly members)",
    category = "PEP",
    listType = "PEP Class 4",
    location = "International",
    webScraperScriptName = "peps",
    updateFrequencyInMinutes = 1440, // Daily
    isActive = true,
    isManualUpdateEnabled = false,
    workflow = "opensanctions"
  )


  val byId: Int => Option[WatchlistSource] = byMember(_.id)

  val byName: String => Option[WatchlistSource] = byMember(_.name)

  def getAll: Set[WatchlistSource] = {
    values.toSet
  }
}
