package me.socure.watchlist.in_house.ingestion.data.extractor.workflow

import com.google.inject.{Inject, Singleton}
import com.typesafe.config.Config
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.watchlist.in_house.ingestion.common.models.enums.WLDataExtractionStatus.WLDataExtractionStatus
import me.socure.watchlist.in_house.ingestion.common.models.enums.{WLDataExtractionStatus, WatchlistSources}
import me.socure.watchlist.in_house.ingestion.common.models.enums.WatchlistSources.WatchlistSource
import me.socure.watchlist.in_house.ingestion.data.extractor.common.Constant._
import me.socure.watchlist.in_house.ingestion.data.extractor.service.S3Service
import org.json4s.{DefaultFormats, Formats, JArray, JObject, JValue}
import org.json4s.jackson.JsonMethods.{compact, parse}
import org.slf4j.LoggerFactory

import java.io.{BufferedWriter, File, FileWriter, RandomAccessFile, OutputStreamWriter, FileOutputStream, InputStreamReader, FileInputStream}
import java.net.URL
import java.nio.charset.StandardCharsets
import java.nio.file.{Files, Paths}
import java.text.SimpleDateFormat
import java.util.Date
import javax.net.ssl.HttpsURLConnection
import com.fasterxml.jackson.core.{JsonFactory, JsonParser, JsonToken}
import com.fasterxml.jackson.databind.{JsonNode, ObjectMapper}
import scala.concurrent.ExecutionContext
import scala.io.{Source, Codec}
import scala.util.{Failure, Success, Try}
import scala.collection.mutable.{Map => MutableMap}

@Singleton
class OpenSanctionsScraperWorkflow @Inject()(config: Config, s3Service: S3Service)(implicit ec: ExecutionContext) extends ScraperWorkflow {

  private val logger = LoggerFactory.getLogger(classOf[OpenSanctionsScraperWorkflow])
  // Use a more specific metrics name to avoid collisions
  private val metrics = JavaMetricsFactory.get("watchlist.in.house.ingestion.data.extractor.workflow.opensanctions")
  private val inHouseS3BucketName = config.getString("watchlist.in.house.s3.bucketName")
  implicit def formats: Formats = DefaultFormats

  // Map to store entity locations for direct file access
  private val entityLocationMap = MutableMap[String, EntityLocation]()

  // Note: We don't need a separate entitySchemaMap since we can get schema from entityLocationMap

  // Map to store entity relationships: entityId -> (propertyName -> Set[targetEntityId])
  private val entityRelationships = MutableMap[String, Map[String, Set[String]]]()

  // Schema-specific indices for faster entity lookups
  private val schemaIndices = MutableMap[String, MutableMap[String, Long]]()

  // JSON parsing utilities for optimized processing
  private val jsonFactory = new JsonFactory()
  private val objectMapper = new ObjectMapper()

  // Counter for processed target entities (used in logging)
  private var processedTargetCount = 0

  // Constants for OpenSanctions
  private val baseUrl = "https://data.opensanctions.org/datasets/latest"
  private val outputDir = s"${entityResolutionInputDataLocalPath}"

  // Create necessary directories
  private def ensureDirectoriesExist(): Unit = {
    Files.createDirectories(Paths.get(outputDir))
  }

  override def downloadSourceData(watchlistSource: WatchlistSource, runId: String, sourceTag: String): Unit = {
    // No need to download source data as we'll fetch it directly from opensanctions.org
    logger.info(s"OpenSanctions workflow doesn't require pre-downloading source data for ${watchlistSource.webScraperScriptName}")
  }

  override def performWebScraperPreProcessing(watchlistSource: WatchlistSources.WatchlistSource, runId: String, sourceTag: String): Unit = {
    // Ensure directories exist
    ensureDirectoriesExist()
    logger.info(s"OpenSanctions preprocessing completed for ${watchlistSource.webScraperScriptName} with runId ${runId}")
  }

  override def performWebScraping(watchlistSource: WatchlistSources.WatchlistSource, runId: String, sourceTag: String): Unit = {
    logger.info(s"Starting OpenSanctions scraping for source id ${watchlistSource.id}, name ${watchlistSource.name} with runId ${runId}")

    // Create temp directory for downloads
    val tempDir = new File(s"${outputDir}/temp_${watchlistSource.webScraperScriptName}")
    if (!tempDir.exists()) {
      tempDir.mkdirs()
    }

    try {
      // Step 1: Get main data from OpenSanctions
      val indexUrl = s"$baseUrl/${watchlistSource.webScraperScriptName}/index.json"
      implicit val formats: Formats = DefaultFormats
      val indexJson = fetchJsonFromUrl(indexUrl)

      // Extract resources from index.json
      val resources = (indexJson \ "resources").extract[List[Map[String, Any]]]

      // Find entities.ftm.json resource
      val entitiesFtmResource = resources.find(_("name") == "entities.ftm.json")

      entitiesFtmResource match {
        case Some(resource) =>
          val entitiesUrl = resource("url").toString
          logger.info(s"Main entities URL: $entitiesUrl")

          // Step 2: Get main entities.ftm.json - download to local file first for reliability
          logger.info("Starting download of main entities data...")
          val localEntitiesFile = new File(tempDir, "main_entities.ftm.json")
          downloadFileToLocal(entitiesUrl, localEntitiesFile.getAbsolutePath)

          logger.info(s"Reading main entities from local file: ${localEntitiesFile.getAbsolutePath}")
          val entitiesLines = Source.fromFile(localEntitiesFile)(Codec.UTF8).getLines()
          logger.info("Successfully obtained main entities data iterator")

          // Step 3: Get enrichment data if available
          val enrichmentEntitiesLines = watchlistSource.enrichmentScriptName match {
            case Some(enrichmentScript) =>
              logger.info(s"Downloading enrichment data from ${enrichmentScript}")
              try {
                val enrichmentIndexUrl = s"$baseUrl/${enrichmentScript}/index.json"
                logger.info(s"Fetching enrichment index from: $enrichmentIndexUrl")
                val enrichmentIndexJson = fetchJsonFromUrl(enrichmentIndexUrl)
                val enrichmentResources = (enrichmentIndexJson \ "resources").extract[List[Map[String, Any]]]
                val enrichmentFtmResource = enrichmentResources.find(_("name") == "entities.ftm.json")

                enrichmentFtmResource match {
                  case Some(enrichmentResource) =>
                    val enrichmentEntitiesUrl = enrichmentResource("url").toString
                    logger.info(s"Fetching enrichment entities from: $enrichmentEntitiesUrl")

                    // Download enrichment file to local disk for reliability
                    val localEnrichmentFile = new File(tempDir, "enrichment_entities.ftm.json")
                    downloadFileToLocal(enrichmentEntitiesUrl, localEnrichmentFile.getAbsolutePath)

                    logger.info(s"Reading enrichment entities from local file: ${localEnrichmentFile.getAbsolutePath}")
                    Some(Source.fromFile(localEnrichmentFile)(Codec.UTF8).getLines())
                  case None =>
                    logger.warn(s"Could not find entities.ftm.json resource for enrichment script ${enrichmentScript}")
                    None
                }
              } catch {
                case e: java.net.SocketException =>
                  logger.error(s"Network error downloading enrichment data from ${enrichmentScript}: ${e.getMessage}")
                  logger.info("Continuing without enrichment data due to network issues")
                  None
                case e: Exception =>
                  logger.warn(s"Error downloading enrichment data from ${enrichmentScript}: ${e.getMessage}")
                  None
              }
            case None =>
              None
          }

          // Step 4-6: Process entities and create output
          processEntities(entitiesLines, watchlistSource, runId, enrichmentEntitiesLines)

        case None =>
          throw new RuntimeException(s"Could not find entities.ftm.json resource for ${watchlistSource.webScraperScriptName}")
      }

      // Cleanup downloaded files
      try {
        val mainFile = new File(tempDir, "main_entities.ftm.json")
        if (mainFile.exists()) {
          mainFile.delete()
          logger.info("Cleaned up main entities file")
        }

        val enrichmentFile = new File(tempDir, "enrichment_entities.ftm.json")
        if (enrichmentFile.exists()) {
          enrichmentFile.delete()
          logger.info("Cleaned up enrichment entities file")
        }
      } catch {
        case e: Exception => logger.warn(s"Error cleaning up downloaded files: ${e.getMessage}")
      }

      logger.info(s"OpenSanctions scraping completed successfully for ${watchlistSource.webScraperScriptName} with runId ${runId}")
    } catch {
      case e: java.net.SocketTimeoutException =>
        logger.error(s"TIMEOUT ERROR during OpenSanctions scraping for ${watchlistSource.webScraperScriptName}")
        logger.error(s"The 801MB+ file download timed out. This is likely due to slow network connection.")
        logger.error(s"Consider running during off-peak hours or using a faster network connection.")
        logger.error(s"Error details: ${e.getMessage}", e)
        try {
          metrics.increment("opensanctions.timeout.error", sourceTag)
        } catch {
          case metricError: Exception =>
            logger.warn(s"Failed to report metrics: ${metricError.getMessage}")
        }
        throw e
      case e: java.net.SocketException =>
        logger.error(s"NETWORK ERROR during OpenSanctions scraping for ${watchlistSource.webScraperScriptName}")
        logger.error(s"This indicates a connection issue with OpenSanctions servers")
        logger.error(s"Error details: ${e.getMessage}", e)
        try {
          metrics.increment("opensanctions.network.error", sourceTag)
        } catch {
          case metricError: Exception =>
            logger.warn(s"Failed to report metrics: ${metricError.getMessage}")
        }
        throw e
      case e: Exception =>
        logger.error(s"GENERAL ERROR during OpenSanctions scraping for ${watchlistSource.webScraperScriptName}: ${e.getMessage}", e)
        try {
          // Use a shorter metric name to avoid UDP packet size issues
          metrics.increment("opensanctions.error", sourceTag)
        } catch {
          case metricError: Exception =>
            logger.warn(s"Failed to report metrics: ${metricError.getMessage}")
        }
        throw e
    }
  }

  override def performWebScraperPostProcessing(watchlistSource: WatchlistSources.WatchlistSource, runId: String, sourceTag: String, wlDataExtractionStatus: WLDataExtractionStatus): Unit = {
    if (wlDataExtractionStatus == WLDataExtractionStatus.DATA_EXTRACTION_SUCCEEDED) {
      logger.info(s"Initiating S3 file upload for runId ${runId} for the destination ${entityResolutionOutputDataS3Path}/${runId}.json")

      try {
        // Upload the processed file to S3
        s3Service.uploadFileToS3(
          inHouseS3BucketName,
          s"${entityResolutionInputDataLocalPath}/${watchlistSource.webScraperScriptName}.json",
          s"${entityResolutionOutputDataS3Path}/${runId}.json"
        )

        logger.info(s"S3 file ${entityResolutionOutputDataS3Path}/${runId}.json uploaded successfully")
      } catch {
        case e: Exception =>
          logger.error(s"Error uploading file to S3 for ${watchlistSource.webScraperScriptName}: ${e.getMessage}", e)
          try {
            // Use a shorter metric name to avoid UDP packet size issues
            metrics.increment("opensanctions.upload.error", sourceTag)
          } catch {
            case metricError: Exception =>
              logger.warn(s"Failed to report metrics: ${metricError.getMessage}")
          }
          throw e
      }
    }
  }

  // Helper method to fetch JSON from a URL with retry logic
  private def fetchJsonFromUrl(url: String): JValue = {
    val maxRetries = 3
    var attempt = 0

    while (attempt < maxRetries) {
      try {
        logger.info(s"Fetching JSON from $url (attempt ${attempt + 1}/$maxRetries)")

        val connection = new URL(url).openConnection().asInstanceOf[HttpsURLConnection]
        connection.setRequestMethod("GET")
        connection.setRequestProperty("User-Agent", "Mozilla/5.0 (compatible; OpenSanctions-Scraper/1.0)")
        connection.setRequestProperty("Accept", "application/json")

        // Set timeouts
        connection.setConnectTimeout(15000) // 15 seconds
        connection.setReadTimeout(30000)    // 30 seconds

        val inputStream = connection.getInputStream
        val content = Source.fromInputStream(inputStream)(Codec.UTF8).mkString
        inputStream.close()
        connection.disconnect()

        return parse(content)

      } catch {
        case e: java.net.SocketException if attempt < maxRetries - 1 =>
          attempt += 1
          val waitTime = attempt * 2000 // Wait 2, 4, 6 seconds
          logger.warn(s"Connection error fetching JSON on attempt $attempt, retrying in ${waitTime/1000} seconds: ${e.getMessage}")
          Thread.sleep(waitTime)

        case e: java.io.IOException if attempt < maxRetries - 1 =>
          attempt += 1
          val waitTime = attempt * 2000
          logger.warn(s"IO error fetching JSON on attempt $attempt, retrying in ${waitTime/1000} seconds: ${e.getMessage}")
          Thread.sleep(waitTime)

        case e: Exception =>
          logger.error(s"Failed to fetch JSON from $url after $maxRetries attempts: ${e.getMessage}")
          throw e
      }
    }

    throw new RuntimeException(s"Failed to fetch JSON from $url after $maxRetries attempts")
  }

  // Helper method to download large file to disk first, then read from local file
  private def downloadFileToLocal(url: String, localPath: String): Unit = {
    val maxRetries = 3
    var attempt = 0

    while (attempt < maxRetries) {
      try {
        logger.info(s"Downloading large file from $url to $localPath (attempt ${attempt + 1}/$maxRetries)")

        val connection = new URL(url).openConnection().asInstanceOf[HttpsURLConnection]
        connection.setRequestMethod("GET")
        connection.setRequestProperty("User-Agent", "Mozilla/5.0 (compatible; OpenSanctions-Scraper/1.0)")
        connection.setRequestProperty("Accept", "application/json")
        connection.setRequestProperty("Connection", "keep-alive")

        // Set longer timeouts for large file downloads
        connection.setConnectTimeout(120000) // 2 minutes to connect
        connection.setReadTimeout(600000)    // 10 minutes to read

        val inputStream = connection.getInputStream
        val outputStream = new FileOutputStream(localPath)
        val buffer = new Array[Byte](8192)

        var totalBytes = 0L
        var bytesRead = 0
        val startTime = System.currentTimeMillis()

        try {
          while ({ bytesRead = inputStream.read(buffer); bytesRead != -1 }) {
            outputStream.write(buffer, 0, bytesRead)
            totalBytes += bytesRead

            // Log progress every 50MB
            if (totalBytes % (50 * 1024 * 1024) == 0) {
              val elapsedSeconds = (System.currentTimeMillis() - startTime) / 1000.0
              val mbDownloaded = totalBytes / (1024.0 * 1024.0)
              val speed = mbDownloaded / elapsedSeconds
              logger.info(f"Downloaded ${mbDownloaded}%.1f MB at ${speed}%.1f MB/s")
            }
          }

          val elapsedSeconds = (System.currentTimeMillis() - startTime) / 1000.0
          val mbDownloaded = totalBytes / (1024.0 * 1024.0)
          logger.info(f"Download completed: ${mbDownloaded}%.1f MB in ${elapsedSeconds}%.1f seconds")

        } finally {
          inputStream.close()
          outputStream.close()
          connection.disconnect()
        }

        return // Success, exit retry loop

      } catch {
        case e: Exception if attempt < maxRetries - 1 =>
          attempt += 1
          val waitTime = attempt * 10000 // Wait 10, 20, 30 seconds
          logger.warn(s"Download failed on attempt $attempt, retrying in ${waitTime/1000} seconds: ${e.getMessage}")
          // Delete partial file if it exists
          val file = new File(localPath)
          if (file.exists()) file.delete()
          Thread.sleep(waitTime)

        case e: Exception =>
          logger.error(s"Failed to download from $url after $maxRetries attempts: ${e.getMessage}")
          throw e
      }
    }
  }

  // Helper method to fetch lines from a URL (for large files like entities.ftm.json) with retry logic
  private def fetchLinesFromUrl(url: String): Iterator[String] = {
    val maxRetries = 3
    var attempt = 0

    while (attempt < maxRetries) {
      try {
        logger.info(s"Downloading from $url (attempt ${attempt + 1}/$maxRetries)")

        val connection = new URL(url).openConnection().asInstanceOf[HttpsURLConnection]
        connection.setRequestMethod("GET")
        connection.setRequestProperty("User-Agent", "Mozilla/5.0 (compatible; OpenSanctions-Scraper/1.0)")
        connection.setRequestProperty("Accept", "application/json")
        connection.setRequestProperty("Connection", "keep-alive")

        // Set timeouts - increased for large files (801MB+)
        connection.setConnectTimeout(60000)  // 60 seconds to connect
        connection.setReadTimeout(300000)    // 5 minutes to read (for large files)

        // Set buffer size for better performance
        connection.setRequestProperty("Accept-Encoding", "gzip, deflate")

        val inputStream = connection.getInputStream
        val bufferedInputStream = new java.io.BufferedInputStream(inputStream, 8192)
        val lines = Source.fromInputStream(bufferedInputStream)(Codec.UTF8).getLines()

        logger.info(s"Successfully connected to $url")

        // Return an iterator that will close the stream when done
        return new Iterator[String] {
          def hasNext: Boolean = {
            try {
              val hasNext = lines.hasNext
              if (!hasNext) {
                try {
                  bufferedInputStream.close()
                  inputStream.close()
                  connection.disconnect()
                  logger.info(s"Successfully completed download from $url")
                } catch {
                  case e: Exception => logger.warn(s"Error closing connection to $url: ${e.getMessage}")
                }
              }
              hasNext
            } catch {
              case e: java.net.SocketTimeoutException =>
                logger.error(s"TIMEOUT ERROR: Read timeout while downloading large file from URL: $url")
                logger.error(s"File appears to be very large (801MB+). Consider increasing timeout or using alternative download method.")
                logger.error(s"Error details: ${e.getMessage}")
                throw e
              case e: java.net.SocketException =>
                logger.error(s"NETWORK ERROR: Connection reset while reading from URL: $url")
                logger.error(s"Error details: ${e.getMessage}")
                throw e
              case e: Exception =>
                logger.error(s"UNEXPECTED ERROR while reading from URL: $url")
                logger.error(s"Error details: ${e.getMessage}")
                throw e
            }
          }

          def next(): String = {
            try {
              lines.next()
            } catch {
              case e: java.net.SocketException =>
                logger.error(s"NETWORK ERROR: Connection reset while reading line from URL: $url")
                logger.error(s"Error details: ${e.getMessage}")
                throw e
              case e: Exception =>
                logger.error(s"UNEXPECTED ERROR while reading line from URL: $url")
                logger.error(s"Error details: ${e.getMessage}")
                throw e
            }
          }
        }

      } catch {
        case e: java.net.SocketTimeoutException if attempt < maxRetries - 1 =>
          attempt += 1
          val waitTime = attempt * 10000 // Wait 10, 20, 30 seconds for timeouts
          logger.warn(s"Read timeout on attempt $attempt (file size: large), retrying in ${waitTime/1000} seconds: ${e.getMessage}")
          Thread.sleep(waitTime)

        case e: java.net.SocketException if attempt < maxRetries - 1 =>
          attempt += 1
          val waitTime = attempt * 5000 // Wait 5, 10, 15 seconds
          logger.warn(s"Connection reset on attempt $attempt, retrying in ${waitTime/1000} seconds: ${e.getMessage}")
          Thread.sleep(waitTime)

        case e: java.io.IOException if attempt < maxRetries - 1 =>
          attempt += 1
          val waitTime = attempt * 5000
          logger.warn(s"IO error on attempt $attempt, retrying in ${waitTime/1000} seconds: ${e.getMessage}")
          Thread.sleep(waitTime)

        case e: Exception =>
          logger.error(s"Failed to download from $url after $maxRetries attempts: ${e.getMessage}")
          throw e
      }
    }

    throw new RuntimeException(s"Failed to download from $url after $maxRetries attempts")
  }

  // Case class to store entity location information
  case class EntityLocation(schema: String, filePath: String, lineNumber: Int, filePosition: Long)

  // Case class for entity metadata - used by optimized JSON parser
  case class EntityMetadata(id: String, schema: String, isTarget: Boolean)

  // Fast extraction of specific fields without parsing the entire JSON
  private def extractEntityMetadata(line: String): EntityMetadata = {
    var id: String = null
    var schema: String = null
    var isTarget: Boolean = false

    val parser = jsonFactory.createParser(line)
    try {
      var token = parser.nextToken()
      while (token != null) {
        val fieldName = parser.getCurrentName()
        if ("id" == fieldName) {
          parser.nextToken()
          id = parser.getValueAsString()
        } else if ("schema" == fieldName) {
          parser.nextToken()
          schema = parser.getValueAsString()
        } else if ("target" == fieldName) {
          parser.nextToken()
          isTarget = parser.getValueAsBoolean()
        }

        // Early exit if we have all we need
        if (id != null && schema != null &&
            parser.getCurrentToken() != null &&
            (parser.getCurrentToken() == JsonToken.VALUE_TRUE ||
             parser.getCurrentToken() == JsonToken.VALUE_FALSE)) {
          // We have all the information we need, break out of the loop
          token = null
        } else {
          token = parser.nextToken()
        }
      }
    } finally {
      parser.close()
    }

    EntityMetadata(
      id = if (id != null) id else "",
      schema = if (schema != null) schema else "",
      isTarget = isTarget
    )
  }

  // Extract entity references from properties for relationship building
  private def extractEntityReferences(jsonStr: String): Map[String, Set[String]] = {
    val references = MutableMap[String, Set[String]]()

    try {
      val rootNode = objectMapper.readTree(jsonStr)
      val propertiesNode = rootNode.get("properties")

      if (propertiesNode != null && propertiesNode.isObject()) {
        val fieldsIterator = propertiesNode.fields()
        while (fieldsIterator.hasNext()) {
          val field = fieldsIterator.next()
          val propertyName = field.getKey()
          val propertyValues = field.getValue()

          if (propertyValues.isArray()) {
            val refIds = (0 until propertyValues.size())
              .map(i => propertyValues.get(i))
              .filter(_.isTextual())
              .map(_.asText())
              .filter(entityLocationMap.contains)
              .toSet

            if (refIds.nonEmpty) {
              references(propertyName) = refIds
            }
          }
        }
      }
    } catch {
      case e: Exception =>
        logger.warn(s"Error extracting entity references: ${e.getMessage}")
    }

    references.toMap
  }

  // Process entities from entities.ftm.json
  private def processEntities(entitiesLines: Iterator[String], watchlistSource: WatchlistSource, runId: String, enrichmentEntitiesLines: Option[Iterator[String]] = None): Unit = {
    implicit val formats: Formats = DefaultFormats

    // Clear any existing data
    entityLocationMap.clear()
    entityRelationships.clear()

    // Set to track target entity IDs
    val targetEntityIds = scala.collection.mutable.Set[String]()

    // Metrics for tracking size reduction
    var originalSize: Long = 0
    var filteredSize: Long = 0

    // Schema counters for logging
    val schemaCounters = MutableMap[String, Int]()
    var totalCount = 0
    var targetCount = 0

    // Create a temporary directory for schema-specific files
    val tempDir = new File(s"${outputDir}/temp_${watchlistSource.webScraperScriptName}")
    if (!tempDir.exists()) {
      tempDir.mkdirs()
    }

    // Map to track schema-specific file writers
    val schemaWriters = MutableMap[String, RandomAccessFile]()

    logger.info(s"First pass: extracting entities for ${watchlistSource.webScraperScriptName}")
    val firstPassStartTime = System.currentTimeMillis()
    var lastLogTime = firstPassStartTime

    try {
      // First pass: extract all entities and build entityLocationMap
      entitiesLines.foreach { line =>
        totalCount += 1

        // Log progress every 10000 entities or 30 seconds, whichever comes first
        val currentTime = System.currentTimeMillis()
        if (totalCount % 10000 == 0 || currentTime - lastLogTime > 30000) {
          val elapsedSeconds = (currentTime - firstPassStartTime) / 1000
          val entitiesPerSecond = totalCount.toDouble / Math.max(1, elapsedSeconds)

          logger.info(f"FIRST PASS PROGRESS: Processed $totalCount entities - $entitiesPerSecond%.2f entities/sec - Elapsed: ${elapsedSeconds/60}%.1f minutes")
          lastLogTime = currentTime
        }

        try {
          // Sanitize the JSON line to remove control characters
          val sanitizedLine = line.replaceAll("[\\p{Cntrl}&&[^\r\n\t]]", "")

          // Use optimized JSON parsing to extract key fields
          val metadata = extractEntityMetadata(sanitizedLine)
          val id = metadata.id
          val schema = metadata.schema
          val isTarget = metadata.isTarget

          if (id.isEmpty || schema.isEmpty) {
            logger.warn(s"Invalid entity data: id=$id, schema=$schema")
            // Skip this entity
          } else {
            // Update schema counter
            schemaCounters(schema) = schemaCounters.getOrElse(schema, 0) + 1

            // Track entities with target schemas
            val targetSchemas = Set("Airplane", "Company", "LegalEntity", "Organization", "Person", "Vessel")
            if (targetSchemas.contains(schema)) {
              targetCount += 1
              targetEntityIds.add(id)
            }

            // Get or create writer for this schema
            val writer = schemaWriters.getOrElseUpdate(schema, {
              val file = new File(s"${tempDir}/${schema}.json")
              new RandomAccessFile(file, "rw")
            })

            // Get current file position before writing
            val filePosition = writer.getFilePointer()

            // Filter out unnecessary fields to reduce storage and processing time
            val filteredEntityJson = filterEntityProperties(sanitizedLine)

            // Track size reduction for metrics
            val originalBytes = sanitizedLine.getBytes(StandardCharsets.UTF_8)
            val filteredBytes = filteredEntityJson.getBytes(StandardCharsets.UTF_8)
            originalSize += originalBytes.length
            filteredSize += filteredBytes.length

            // Write filtered entity to schema-specific file with UTF-8 encoding
            writer.write(filteredBytes)
            writer.write('\n')

            // Store entity location with file position
            entityLocationMap(id) = EntityLocation(schema, s"${tempDir}/${schema}.json", totalCount, filePosition)

            // Update schema-specific index for faster lookups
            updateSchemaIndex(schema, id, filePosition)
          } // Close the else block
        } catch {
          case e: Exception =>
            logger.warn(s"Error processing entity line: ${e.getMessage}")
        }
      }

      // Log statistics after first pass
      // Log schema index statistics
      logger.info(s"Schema indices built: ${schemaIndices.size} schemas, ${schemaIndices.values.map(_.size).sum} total entries")
      schemaIndices.foreach { case (schema, index) =>
        logger.info(s"Schema index for $schema: ${index.size} entries")
      }

      // Log size reduction metrics
      val reductionPercent = if (originalSize > 0) 100 - (filteredSize.toDouble / originalSize.toDouble * 100) else 0
      logger.info(f"Entity size reduction: ${reductionPercent}%.2f%% (Original: ${originalSize / (1024 * 1024)}MB, Filtered: ${filteredSize / (1024 * 1024)}MB)")

      logger.info(s"First pass completed: processed ${totalCount} entities, found ${targetCount} target entities for ${watchlistSource.webScraperScriptName}")
      logger.info(s"Entity counts by schema: ${schemaCounters.map { case (schema, count) => s"$schema: $count" }.mkString(", ")}")

      // Second pass: build relationships after all entities are known
      logger.info(s"Second pass: building relationships for ${watchlistSource.webScraperScriptName}")
      val secondPassStartTime = System.currentTimeMillis()
      lastLogTime = secondPassStartTime
      var processedCount = 0

      // Process all entities to build relationships
      entityLocationMap.keys.foreach { id =>
        processedCount += 1

        // Log progress every 10000 entities or 30 seconds
        val currentTime = System.currentTimeMillis()
        if (processedCount % 10000 == 0 || currentTime - lastLogTime > 30000) {
          val elapsedSeconds = (currentTime - secondPassStartTime) / 1000
          val entitiesPerSecond = processedCount.toDouble / Math.max(1, elapsedSeconds)
          val percentComplete = (processedCount.toDouble / entityLocationMap.size) * 100

          logger.info(f"SECOND PASS PROGRESS: Processed $processedCount of ${entityLocationMap.size} entities ($percentComplete%.2f%%) - $entitiesPerSecond%.2f entities/sec - Elapsed: ${elapsedSeconds/60}%.1f minutes")
          lastLogTime = currentTime
        }

        try {
          // Get entity location
          val location = getEntityLocation(id)

          // Read entity from file
          val entityJson = readEntityAsString(location.filePath, id)

          // Use optimized method to extract entity references
          val references = extractEntityReferences(entityJson)

          // Add relationships for each reference
          references.foreach { case (propertyName, refIds) =>
            refIds.foreach { refId =>
              // Add relationship
              addRelationship(id, refId, propertyName)
            }
          }
        } catch {
          case e: Exception =>
            logger.warn(s"Error building relationships for entity $id: ${e.getMessage}")
        }
      }

      // Log statistics after second pass
      logger.info(s"Second pass completed: processed ${processedCount} entities, built relationships for ${entityRelationships.size} entities")

      // Log some sample relationships for debugging
      entityRelationships.take(5).foreach { case (id, relationships) =>
        val location = entityLocationMap.get(id)
        val schema = location.map(_.schema).getOrElse("unknown")
        logger.info(s"DEBUG: Entity $id ($schema) has relationships: ${relationships.map { case (prop, targets) => s"$prop: ${targets.size}" }.mkString(", ")}")
      }

      // Process enrichment data if available (for data enrichment only - no new entities added to target list)
      enrichmentEntitiesLines.foreach { enrichmentLines =>
        logger.info("Starting enrichment data processing")
        var enrichmentCount = 0
        var totalEnrichmentEntities = 0
        val enrichmentWriters = scala.collection.mutable.Map[String, BufferedWriter]()
        val enrichmentSchemaStats = scala.collection.mutable.Map[String, Int]()
        val enrichmentMatchStats = scala.collection.mutable.Map[String, Int]()

        try {
          enrichmentLines.foreach { line =>
            try {
              val entity = parse(line)
              val id = (entity \ "id").extract[String]
              val schema = (entity \ "schema").extract[String]

              totalEnrichmentEntities += 1
              enrichmentSchemaStats(schema) = enrichmentSchemaStats.getOrElse(schema, 0) + 1

              // Only process enrichment data for entities that already exist in our main data
              if (entityLocationMap.contains(id)) {
                enrichmentMatchStats(schema) = enrichmentMatchStats.getOrElse(schema, 0) + 1

                // Log first few matches for each schema for debugging
                if (enrichmentMatchStats(schema) <= 3) {
                  logger.info(s"MATCH FOUND: Schema=$schema, ID=$id, Caption=${(entity \ "caption").extractOpt[String].getOrElse("N/A")}")
                }

                // Store enrichment entity in a separate schema file for potential future use
                val enrichmentWriter = enrichmentWriters.getOrElseUpdate(s"${schema}_enrichment", {
                  val file = new File(s"${tempDir}/${schema}_enrichment.json")
                  logger.info(s"Creating enrichment file: ${file.getAbsolutePath}")
                  new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file), StandardCharsets.UTF_8))
                })

                enrichmentWriter.write(line)
                enrichmentWriter.newLine()
                enrichmentCount += 1

                if (enrichmentCount % 1000 == 0) {
                  logger.info(s"Processed $enrichmentCount enrichment entities (matched from $totalEnrichmentEntities total)")
                }
              }

              // Log schema statistics every 50000 entities
              if (totalEnrichmentEntities % 50000 == 0) {
                logger.info(s"Enrichment progress: $totalEnrichmentEntities entities processed")
                logger.info(s"Schema distribution: ${enrichmentSchemaStats.map { case (k, v) => s"$k: $v" }.mkString(", ")}")
                logger.info(s"Matched entities: ${enrichmentMatchStats.map { case (k, v) => s"$k: $v" }.mkString(", ")}")
              }
            } catch {
              case e: java.net.SocketException =>
                logger.error(s"Network error during enrichment processing: ${e.getMessage}")
                logger.info("Stopping enrichment processing due to network issues")
                return // Exit the foreach loop
              case e: Exception =>
                logger.warn(s"Error processing enrichment entity: ${e.getMessage}")
            }
          }
        } catch {
          case e: java.net.SocketException =>
            logger.error(s"Network error during enrichment processing: ${e.getMessage}")
            logger.info(s"Enrichment processing stopped due to network issues. Processed $enrichmentCount entities so far.")
        } finally {
          // Close enrichment writers and log file information
          enrichmentWriters.foreach { case (schemaName, writer) =>
            try {
              writer.close()
              val file = new File(s"${tempDir}/${schemaName}.json")
              if (file.exists()) {
                logger.info(s"Enrichment file created: ${file.getAbsolutePath} (${file.length()} bytes)")
              } else {
                logger.warn(s"Enrichment file not found: ${file.getAbsolutePath}")
              }
            } catch {
              case e: Exception => logger.warn(s"Error closing enrichment writer for $schemaName: ${e.getMessage}")
            }
          }
        }

        logger.info(s"Enrichment data processing completed. Processed $enrichmentCount enrichment entities from $totalEnrichmentEntities total")
        logger.info(s"Final schema distribution in enrichment data: ${enrichmentSchemaStats.map { case (k, v) => s"$k: $v" }.mkString(", ")}")
        logger.info(s"Final matched entities by schema: ${enrichmentMatchStats.map { case (k, v) => s"$k: $v" }.mkString(", ")}")
        logger.info(s"Enrichment files created: ${enrichmentWriters.keys.mkString(", ")}")
      }

      // Third pass: process entities with target schemas and write to output file
      logger.info(s"Third pass: processing entities with target schemas for ${watchlistSource.webScraperScriptName}")
      val thirdPassStartTime = System.currentTimeMillis()
      lastLogTime = thirdPassStartTime
      processedTargetCount = 0
      var skippedTargetCount = 0

      // Create output file
      val outputFile = new File(s"${outputDir}/${watchlistSource.webScraperScriptName}.json")
      val writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outputFile), StandardCharsets.UTF_8))

      try {
        // Process entities in batches to avoid memory issues
        val batchSize = 1000
        val entityBatches = targetEntityIds.grouped(batchSize).toList
        val totalBatches = entityBatches.size
        val totalTargets = targetEntityIds.size

        logger.info(s"Processing ${targetEntityIds.size} entities with target schemas in $totalBatches batches of $batchSize")

        entityBatches.zipWithIndex.foreach { case (batch, batchIndex) =>
          logger.info(s"Processing batch ${batchIndex + 1} of $totalBatches")

          batch.foreach { id =>
            // Log progress every 100 entities or 30 seconds
            val currentTime = System.currentTimeMillis()
            if ((processedTargetCount + skippedTargetCount) % 100 == 0 || currentTime - lastLogTime > 30000) {
              val percentComplete = ((processedTargetCount + skippedTargetCount).toDouble / totalTargets) * 100
              val elapsedSeconds = (currentTime - thirdPassStartTime) / 1000
              val entitiesPerSecond = (processedTargetCount + skippedTargetCount).toDouble / Math.max(1, elapsedSeconds)
              val estimatedTotalSeconds = totalTargets / Math.max(1, entitiesPerSecond)
              val estimatedRemainingSeconds = estimatedTotalSeconds - elapsedSeconds

              logger.info(f"PROGRESS: Processed $processedTargetCount of $totalTargets entities with target schemas ($percentComplete%.2f%%) - $entitiesPerSecond%.2f entities/sec - Est. remaining: ${estimatedRemainingSeconds/60}%.1f minutes - Skipped: $skippedTargetCount")
              lastLogTime = currentTime
            }

            try {
                // Get target entity location
                val targetLocation = getEntityLocation(id)

                // Read target entity from file
                val targetEntityJson = readEntityAsString(targetLocation.filePath, id)
                val targetEntity = parse(targetEntityJson)

                // Find linked entity IDs using our relationship registry
                val linkedEntityIds = findAllLinkedEntityIds(id)

                if (linkedEntityIds.nonEmpty && (processedTargetCount % 1000 == 0)) {
                  logger.info(s"DEBUG: Entity $id has ${linkedEntityIds.size} linked entities")
                }

                // Read linked entities from their files
                val linkedEntities = linkedEntityIds.flatMap { linkedId =>
                  try {
                    // Get entity location
                    val location = getEntityLocation(linkedId)

                    // Read entity from file
                    val entityJson = readEntityAsString(location.filePath, linkedId)
                    val entity = parse(entityJson)

                      // Log entity schema for debugging
                      val entitySchema = (entity \ "schema").extract[String]
                      if (entitySchema == "Position" && processedTargetCount % 1000 == 0) {
                        logger.info(s"DEBUG: Including Position entity $linkedId in linked entities")
                      }

                      Some(entity)
                  } catch {
                    case e: Exception =>
                      logger.warn(s"Error loading linked entity $linkedId: ${e.getMessage}")
                      None
                  }
                }.toList

                // Log the schemas of linked entities
                if (linkedEntities.nonEmpty && processedTargetCount % 1000 == 0) {
                  val linkedSchemas = linkedEntities.map(e => (e \ "schema").extract[String]).groupBy(identity).map { case (schema, list) =>
                    s"$schema: ${list.size}"
                  }.mkString(", ")
                  logger.info(s"DEBUG: Linked entity schemas for $id: $linkedSchemas")
                }

                // Combine entity with linked entities
                val combinedEntity = combineWithLinkedEntities(targetEntity, linkedEntities)

                // Only write if entity has PEP class (combinedEntity is not null)
                if (combinedEntity != null) {
                  // Write to output file
                  writer.write(compact(combinedEntity))
                  writer.newLine()
                  processedTargetCount += 1
                } else {
                  // Entity was skipped due to no PEP class
                  skippedTargetCount += 1
                }
            } catch {
                case e: Exception =>
                    logger.warn(s"Error processing entity $id: ${e.getMessage}")
                    skippedTargetCount += 1
            }
          }
        }

        // Create metadata file required by DataExtractorService
        createMetadataFile(watchlistSource, processedTargetCount)

        logger.info(s"Processed ${processedTargetCount} entities with target schemas for ${watchlistSource.webScraperScriptName}, runId ${runId}")
        logger.info(s"Skipped ${skippedTargetCount} entities (errors + no PEP class)")
      } finally {
        writer.close()

        // Keep temporary directory for debugging
        logger.info(s"Keeping temporary directory for debugging: ${tempDir.getAbsolutePath()}")
      }
    } finally {
      // Close all schema writers
      schemaWriters.values.foreach(writer => {
        try {
          writer.close()
        } catch {
          case e: Exception => logger.warn(s"Error closing file writer: ${e.getMessage}")
        }
      })
    }

    // Helper method to add a relationship
    def addRelationship(sourceId: String, targetId: String, propertyName: String): Unit = {
      // Skip self-references to prevent entities from being linked to themselves
      if (sourceId == targetId) {
        return
      }

      val relationships = entityRelationships.getOrElse(sourceId, Map.empty[String, Set[String]])
      val targets = relationships.getOrElse(propertyName, Set.empty[String])
      entityRelationships(sourceId) = relationships + (propertyName -> (targets + targetId))

      // Also add reverse relationship
      val reversePropertyName = s"reverse_$propertyName"
      val reverseRelationships = entityRelationships.getOrElse(targetId, Map.empty[String, Set[String]])
      val sources = reverseRelationships.getOrElse(reversePropertyName, Set.empty[String])
      entityRelationships(targetId) = reverseRelationships + (reversePropertyName -> (sources + sourceId))
    }
  }
  // Read an entity from a file by ID using direct file access with UTF-8 encoding
  private def readEntityFromFile(filePath: String, entityId: String): JValue = {
    implicit val formats: Formats = DefaultFormats

    // Get the entity location from the map
    val entityLocation = entityLocationMap.get(entityId).getOrElse(
      throw new RuntimeException(s"Entity $entityId not found in location map"))

    val startTime = System.currentTimeMillis()
    val randomAccessFile = new RandomAccessFile(filePath, "r")

    try {
      // Jump directly to the entity's position in the file
      randomAccessFile.seek(entityLocation.filePosition)

      // Read the line at that position
      val line = randomAccessFile.readLine()

      if (line == null) {
        throw new RuntimeException(s"Null line read for entity $entityId at position ${entityLocation.filePosition} in file $filePath")
      }

      // Convert bytes to UTF-8 string and sanitize
      val utf8Line = new String(line.getBytes("ISO-8859-1"), StandardCharsets.UTF_8)
      val sanitizedLine = utf8Line.replaceAll("[\\p{Cntrl}&&[^\r\n\t]]", "")

      // Parse the entity
      val entity = parse(sanitizedLine)

      // Verify it's the correct entity
      val id = (entity \ "id").extract[String]
      if (id != entityId) {
        logger.warn(s"Entity ID mismatch: expected $entityId but found $id at position ${entityLocation.filePosition}")
        throw new RuntimeException(s"Entity ID mismatch at position ${entityLocation.filePosition} in file $filePath")
      }

      val endTime = System.currentTimeMillis()
      val timeMs = endTime - startTime

      // Only log if it took more than 10ms to find the entity
      if (timeMs > 10 && processedTargetCount % 1000 == 0) { // Reduce log volume
        logger.info(s"ENTITY LOAD: Found entity $entityId in ${timeMs}ms using direct file access from $filePath")
      }

      entity
    } catch {
      case e: Exception =>
        val endTime = System.currentTimeMillis()
        logger.warn(s"ENTITY LOAD: Failed to load entity $entityId in ${endTime - startTime}ms from $filePath: ${e.getMessage}")
        throw e
    } finally {
      randomAccessFile.close()
    }
  }

  // Find all entity IDs linked to the given entity using the relationship registry
  private def findAllLinkedEntityIds(entityId: String): Set[String] = {
    // Set to collect linked entity IDs
    val linkedEntityIds = scala.collection.mutable.Set[String]()

    // Get all relationships for this entity
    val relationships = entityRelationships.getOrElse(entityId, Map.empty[String, Set[String]])

    // Add all directly linked entities (from all properties)
    relationships.values.foreach(linkedEntityIds ++= _)

    // Get the schema of the target entity
    val targetLocation = entityLocationMap.get(entityId)
    val targetSchema = targetLocation.map(_.schema).getOrElse("")

    // For Person entities, also include second-level relationships
    if (targetSchema == "Person") {
      // Find all Occupancy and Membership entities linked to this Person
      val intermediateEntities = linkedEntityIds.filter { linkedId =>
        val location = entityLocationMap.get(linkedId)
        val schema = location.map(_.schema).getOrElse("")
        schema == "Occupancy" || schema == "Membership"
      }

      // For each intermediate entity, add its linked entities
      val secondLevelLinks = scala.collection.mutable.Set[String]()
      intermediateEntities.foreach { intermediateId =>
        // Get relationships for this intermediate entity
        val intermediateRelationships = entityRelationships.getOrElse(intermediateId, Map.empty[String, Set[String]])

        // Add all entities linked to this intermediate entity
        intermediateRelationships.values.foreach(secondLevelLinks ++= _)

        // Log for debugging
        val intermediateLocation = entityLocationMap.get(intermediateId)
        val intermediateSchema = intermediateLocation.map(_.schema).getOrElse("unknown")
        if (processedTargetCount % 1000 == 0) {
          logger.info(s"DEBUG: Adding second-level links from $intermediateSchema $intermediateId: ${secondLevelLinks.size} entities")
        }
      }

      // Add second-level links
      linkedEntityIds ++= secondLevelLinks
    }

    // Log the results
    if (processedTargetCount % 1000 == 0) {
      logger.info(s"DEBUG: Found ${linkedEntityIds.size} linked entities for $entityId")

      // Log some sample linked entities for debugging
      val sampleLinkedEntities = linkedEntityIds.take(5).map { id =>
        val location = entityLocationMap.get(id)
        val schema = location.map(_.schema).getOrElse("unknown")
        s"$id ($schema)"
      }.mkString(", ")
      logger.info(s"DEBUG: Sample linked entities for $entityId: $sampleLinkedEntities")
    }

    linkedEntityIds.toSet
  }

  // Combine an entity with its linked entities and transform to Senzing format
  private def combineWithLinkedEntities(entity: JValue, linkedEntities: List[JValue]): JValue = {
    import org.json4s.JsonDSL._
    import org.json4s.jackson.JsonMethods._
    import org.json4s.jackson.Serialization

    // Define implicit formats for JSON extraction
    implicit val formats: Formats = DefaultFormats

    // Transform to Senzing format
    val senzingFormatJson = transformToSenzingFormat(entity, linkedEntities)

    // Check if entity was skipped (null return)
    if (senzingFormatJson == null) {
      null
    } else {
      // Parse back to JValue
      parse(senzingFormatJson)
    }
  }

  // New method to transform OpenSanctions entity to Senzing format
  private def transformToSenzingFormat(entity: JValue, linkedEntities: List[JValue]): String = {
    import org.json4s.JsonDSL._
    import org.json4s.jackson.JsonMethods._
    import org.json4s.jackson.Serialization
    import org.json4s.jackson.Serialization.{read, write}
    import org.json4s.Extraction

    // Define implicit formats for JSON extraction
    implicit val formats: Formats = DefaultFormats

    // Extract entity ID and schema
    val id = (entity \ "id").extract[String]
    val schema = (entity \ "schema").extract[String]
    val properties = (entity \ "properties").extract[Map[String, List[String]]]

    // Determine entity type for Senzing
    val sourceEntityType = schema match {
      case "Person" => "Individual"
      case "Organization" | "Company" | "LegalEntity" | "PublicBody" => "Organization"
      case "Airplane" | "Vessel" => "Individual" // Treat vehicles as individuals for Senzing
      case _ => "Individual" // Default to Individual for other types
    }

    // Create the base structure with mutable maps
    var jsonData = scala.collection.mutable.Map[String, Any](
      "SOURCE_ENTITY_TYPE" -> sourceEntityType
    )

    // Add name information
    val nameList = extractNames(schema, properties)
    if (nameList.nonEmpty) {
      jsonData += ("NAME_LIST" -> nameList)
    }

    // Add date of birth information
    val dobList = extractDatesOfBirth(properties)
    if (dobList.nonEmpty) {
      jsonData += ("DATE_OF_BIRTH_LIST" -> dobList)
    }

    // Add address information
    val addressList = extractAddresses(properties)
    if (addressList.nonEmpty && addressList.exists(_("ADDR_FULL").nonEmpty)) {
      jsonData += ("ADDRESS_LIST" -> addressList)
    }

    // Add country information
    val countryList = extractCountries(properties)
    if (countryList.nonEmpty) {
      jsonData += ("COUNTRY_LIST" -> countryList)
    }

    // Add other information as JSON string
    val otherInfo = extractOtherInfo(entity, linkedEntities)
    jsonData += ("OTHER" -> otherInfo)

    // Determine and add PEP class at JSON_DATA level
    val otherInfoMap = scala.collection.mutable.Map[String, Any]()

    // Parse the otherInfo to extract topics for PEP class determination
    try {
      val otherData = parse(otherInfo).extract[Map[String, Any]]
      otherInfoMap ++= otherData
    } catch {
      case e: Exception => logger.warn(s"Error parsing OTHER info for PEP class determination: ${e.getMessage}")
    }

    // Add main entity properties to the map for PEP class determination
    properties.foreach { case (key, values) =>
      otherInfoMap += (key -> values)
    }

    val pepClass = determinePepClass(otherInfoMap)
    pepClass match {
      case Some(classes) =>
        jsonData += ("PEP_CLASS" -> classes)
//        logger.info(s"Added PEP_CLASS $classes for entity $id")

        // Create the final entity structure only if PEP class is found
        val senzingEntity = Map(
          "DATA_SOURCE" -> "SOCURE_WLS",
          "RECORD_ID" -> id,
          "RECORD_STATUS" -> "Add", // Default to Add
          "JSON_DATA" -> jsonData.toMap
        )

        // Convert to JSON string
        compact(render(Extraction.decompose(senzingEntity)))

      case None =>
//        logger.info(s"No PEP class determined for entity $id - skipping entity")
        // Return null to indicate this entity should be skipped
        null
    }
  }

  // Helper methods to extract specific information
  private def extractNames(schema: String, properties: Map[String, List[String]]): List[Map[String, String]] = {
    val nameList = scala.collection.mutable.ListBuffer[Map[String, String]]()

    // Add primary name
    if (properties.contains("name") && properties("name").nonEmpty) {
      val nameType = schema match {
        case "Person" => "INDIVIDUAL_NAME"
        case "Organization" | "Company" | "LegalEntity" | "PublicBody" => "ORGANISATION_NAME"
        case "Airplane" | "Vessel" => "INDIVIDUAL_NAME" // Treat vehicles as individuals
        case _ => "INDIVIDUAL_NAME"
      }

      val nameField = schema match {
        case "Person" => "NAME_FULL"
        case "Organization" | "Company" | "LegalEntity" | "PublicBody" => "NAME_ORG"
        case "Airplane" | "Vessel" => "NAME_FULL" // Treat vehicles as individuals
        case _ => "NAME_FULL"
      }

      nameList += Map(
        "NAME_TYPE" -> nameType,
        nameField -> properties("name").head
      )
    }

    // Add aliases
    if (properties.contains("alias")) {
      properties("alias").foreach { alias =>
        if (alias.nonEmpty) {
          nameList += Map(
            "NAME_TYPE" -> "AKA",
            "NAME_FULL" -> alias
          )
        }
      }
    }

    // Add weak aliases
    if (properties.contains("weakAlias")) {
      properties("weakAlias").foreach { alias =>
        if (alias.nonEmpty) {
          nameList += Map(
            "NAME_TYPE" -> "AKA",
            "NAME_FULL" -> alias
          )
        }
      }
    }

    // Add previous names
    if (properties.contains("previousName")) {
      properties("previousName").foreach { alias =>
        if (alias.nonEmpty) {
          nameList += Map(
            "NAME_TYPE" -> "AKA",
            "NAME_FULL" -> alias
          )
        }
      }
    }

    // Don't add empty AKA entries

    nameList.toList
  }

  private def extractDatesOfBirth(properties: Map[String, List[String]]): List[Map[String, String]] = {
    val dobList = scala.collection.mutable.ListBuffer[Map[String, String]]()

    if (properties.contains("birthDate")) {
      properties("birthDate").foreach { dob =>
        if (dob.nonEmpty) {
          // Format date as YYYY-MM-DD
          val formattedDob = formatDate(dob)
          dobList += Map(
            "DATE_OF_BIRTH" -> formattedDob
          )
        }
      }
    }

    dobList.toList
  }

  private def extractAddresses(properties: Map[String, List[String]]): List[Map[String, String]] = {
    val addressList = scala.collection.mutable.ListBuffer[Map[String, String]]()

    // Extract address from properties
    if (properties.contains("address")) {
      properties("address").foreach { addr =>
        if (addr.nonEmpty) {
          addressList += Map(
            "ADDR_FULL" -> addr
          )
        }
      }
    }

    addressList.toList
  }

  private def extractCountries(properties: Map[String, List[String]]): List[Map[String, String]] = {
    val countryList = scala.collection.mutable.ListBuffer[Map[String, String]]()

    // Extract nationality
    if (properties.contains("nationality")) {
      properties("nationality").foreach { country =>
        if (country.nonEmpty) {
          countryList += Map(
            "CITIZENSHIP" -> country.toUpperCase
          )
        }
      }
    }

    // Extract citizenship
    if (properties.contains("citizenship")) {
      properties("citizenship").foreach { country =>
        if (country.nonEmpty && !countryList.exists(_("CITIZENSHIP") == country.toUpperCase)) {
          countryList += Map(
            "CITIZENSHIP" -> country.toUpperCase
          )
        }
      }
    }

    // Extract country
    if (properties.contains("country")) {
      properties("country").foreach { country =>
        if (country.nonEmpty && !countryList.exists(_("CITIZENSHIP") == country.toUpperCase)) {
          countryList += Map(
            "CITIZENSHIP" -> country.toUpperCase
          )
        }
      }
    }

    countryList.toList
  }

  private def extractOtherInfo(entity: JValue, linkedEntities: List[JValue]): String = {
    import java.text.SimpleDateFormat
    import java.util.Date
    import org.json4s.JsonDSL._
    import org.json4s.jackson.JsonMethods._
    import org.json4s.jackson.Serialization
    import org.json4s.jackson.Serialization.{read, write}
    import org.json4s.Extraction

    // Define implicit formats for JSON extraction
    implicit val formats: Formats = DefaultFormats

    // Create a map to hold all other information
    val otherInfo = scala.collection.mutable.Map[String, Any]()

    // Don't add DATAID - not needed

    // Extract all properties
    val properties = (entity \ "properties").extract[Map[String, List[String]]]

    // Extract source information from properties
    val sourceUrl = properties.getOrElse("sourceUrl", List()).headOption
    val publisher = properties.getOrElse("publisher", List()).headOption.getOrElse("OpenSanctions")
    val program = properties.getOrElse("program", List()).headOption.getOrElse("")

    // Don't add SourceName and SourceCode - not needed

    // Only add URL if it exists in the properties
    sourceUrl.foreach { url =>
      otherInfo += ("Url" -> url)
    }

    // Add program information if available
    if (program.nonEmpty) {
      otherInfo += ("program" -> List(program))
    }

    // Add all other properties except those already handled and entity ID fields
    properties.foreach { case (key, values) =>
      if (!Set("name", "alias", "weakAlias", "previousName", "address", "nationality",
               "citizenship", "country", "birthDate", "sourceUrl", "publisher").contains(key) &&
          !key.endsWith("Entity")) {
        otherInfo += (key -> values)
      }
    }

    // Add sanction information if available
    if (properties.contains("sanctions")) {
      otherInfo += ("sanctions" -> properties("sanctions"))
    }

    // Add linked entities information as flattened fields
    val linkedEntitiesBySchema = linkedEntities.groupBy(e => (e \ "schema").extract[String])

    linkedEntitiesBySchema.foreach { case (schema, entities) =>
      entities.foreach { linkedEntity =>
        val linkedProperties = (linkedEntity \ "properties").extract[Map[String, List[String]]]
        val caption = (linkedEntity \ "caption").extractOpt[String]

        // Extract specific properties based on schema type
        schema match {
          case "Sanction" =>
            // Add caption if available
            caption.foreach { cap =>
              otherInfo += ("Sanction" -> List(cap))
            }
            linkedProperties.get("program").foreach { programs =>
              otherInfo += ("SanctionProgram" -> programs)
            }
            linkedProperties.get("startDate").foreach { startDates =>
              otherInfo += ("SanctionStartDate" -> startDates)
            }
            linkedProperties.get("endDate").foreach { endDates =>
              otherInfo += ("SanctionEndDate" -> endDates)
            }
            linkedProperties.get("authority").foreach { authorities =>
              otherInfo += ("SanctionAuthority" -> authorities)
            }
            linkedProperties.get("sourceUrl").foreach { sourceUrls =>
              otherInfo += ("SanctionSourceUrl" -> sourceUrls)
            }
            // Add all other properties with Sanction prefix
            linkedProperties.foreach { case (key, values) =>
              if (!Set("program", "startDate", "endDate", "authority", "sourceUrl").contains(key)) {
                otherInfo += (s"Sanction${key.capitalize}" -> values)
              }
            }

          case "Passport" =>
            // Add caption if available
            caption.foreach { cap =>
              otherInfo += ("Passport" -> List(cap))
            }
            linkedProperties.get("number").foreach { numbers =>
              otherInfo += ("PassportNumber" -> numbers)
            }
            linkedProperties.get("country").foreach { countries =>
              otherInfo += ("PassportCountry" -> countries)
            }
            // Add all other properties with Passport prefix
            linkedProperties.foreach { case (key, values) =>
              if (!Set("number", "country").contains(key)) {
                otherInfo += (s"Passport${key.capitalize}" -> values)
              }
            }

          case "Position" =>
            // Add caption if available
            caption.foreach { cap =>
              otherInfo += ("Position" -> List(cap))
            }
            linkedProperties.get("name").foreach { names =>
              otherInfo += ("PositionName" -> names)
            }
            linkedProperties.get("organization").foreach { orgs =>
              otherInfo += ("PositionOrganization" -> orgs)
            }
            // Add all other properties with Position prefix
            linkedProperties.foreach { case (key, values) =>
              if (!Set("name", "organization").contains(key)) {
                otherInfo += (s"Position${key.capitalize}" -> values)
              }
            }

          case "Address" =>
            // Add caption if available
            caption.foreach { cap =>
              otherInfo += ("Address" -> List(cap))
            }
            linkedProperties.get("full").foreach { addresses =>
              otherInfo += ("AddressFull" -> addresses)
            }
            linkedProperties.get("country").foreach { countries =>
              otherInfo += ("AddressCountry" -> countries)
            }
            // Add all other properties with Address prefix
            linkedProperties.foreach { case (key, values) =>
              if (!Set("full", "country").contains(key)) {
                otherInfo += (s"Address${key.capitalize}" -> values)
              }
            }

          case "Succession" =>
            // Add caption if available
            caption.foreach { cap =>
              otherInfo += ("Succession" -> List(cap))
            }
            // For Succession, resolve entity IDs to their names/captions
            linkedProperties.get("predecessor").foreach { predecessorIds =>
              val predecessorNames = predecessorIds.flatMap { id =>
                entityLocationMap.get(id).flatMap { location =>
                  try {
                    val entityJson = readEntityAsString(location.filePath, id)
                    val entity = parse(entityJson)
                    (entity \ "caption").extractOpt[String].orElse(
                      (entity \ "properties" \ "name").extractOpt[List[String]].flatMap(_.headOption)
                    )
                  } catch {
                    case _: Exception => None
                  }
                }
              }
              if (predecessorNames.nonEmpty) {
                otherInfo += ("SuccessionPredecessor" -> predecessorNames)
              }
            }
            linkedProperties.get("successor").foreach { successorIds =>
              val successorNames = successorIds.flatMap { id =>
                entityLocationMap.get(id).flatMap { location =>
                  try {
                    val entityJson = readEntityAsString(location.filePath, id)
                    val entity = parse(entityJson)
                    (entity \ "caption").extractOpt[String].orElse(
                      (entity \ "properties" \ "name").extractOpt[List[String]].flatMap(_.headOption)
                    )
                  } catch {
                    case _: Exception => None
                  }
                }
              }
              if (successorNames.nonEmpty) {
                otherInfo += ("SuccessionSuccessor" -> successorNames)
              }
            }

          case _ =>
            // For other schemas, add caption and properties with schema prefix
            caption.foreach { cap =>
              otherInfo += (schema -> List(cap))
            }
            linkedProperties.foreach { case (key, values) =>
              otherInfo += (s"${schema}${key.capitalize}" -> values)
            }
        }
      }
    }

    // Add effective date (using designationDate if available)
    if (properties.contains("designationDate")) {
      otherInfo += ("EffectiveOrConvictionDate" -> properties("designationDate").head)
    } else if (properties.contains("listingDate")) {
      otherInfo += ("EffectiveOrConvictionDate" -> properties("listingDate").head)
    } else {
      // Use current date as fallback
      val dateFormat = new SimpleDateFormat("yyyy-MM-dd")
      otherInfo += ("EffectiveOrConvictionDate" -> dateFormat.format(new Date()))
    }

    // Add latest update date
    val dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss")
    otherInfo += ("LatestUpdate" -> dateFormat.format(new Date()))

    // Add addition date
    otherInfo += ("AdditionDate" -> dateFormat.format(new Date()))

    // Add NewAoD flag
    otherInfo += ("NewAoD" -> true)

    // Remove PEP class from OTHER - it will be added to main JSON_DATA

    // Convert to JSON string
    compact(render(Extraction.decompose(otherInfo)))
  }

  // Helper method to format dates
  // Helper method to determine PEP class based on jurisdiction and role
  private def determinePepClass(otherInfo: scala.collection.mutable.Map[String, Any]): Option[List[String]] = {
    // Extract topics from various sources
    val allTopics = scala.collection.mutable.Set[String]()

    // Add topics from main entity
    otherInfo.get("topics") match {
      case Some(topics: List[_]) => topics.foreach(t => allTopics += t.toString)
      case _ =>
    }

    // Add topics from linked entities (Position, Person, etc.)
    otherInfo.keys.filter(_.endsWith("Topics")).foreach { key =>
      otherInfo.get(key) match {
        case Some(topics: List[_]) => topics.foreach(t => allTopics += t.toString)
        case _ =>
      }
    }

    // Debug logging - temporarily using info level
    if (allTopics.nonEmpty) {
    }

    // Determine PEP class based on jurisdiction hierarchy (most specific first)
    if (allTopics.contains("gov.national")) {
      Some(List("Pep Class 1", "Pep Class 2"))
    } else if (allTopics.contains("gov.igo")) {
      Some(List("Pep Class 1"))
    } else if (allTopics.contains("gov.state")) {
      Some(List("Pep Class 2"))
    } else if (allTopics.contains("gov.soe")) {
      Some(List("Pep Class 3"))
    } else if (allTopics.contains("gov.muni")) {
      Some(List("Pep Class 4"))
    } else {
      None // No matching jurisdiction found
    }
  }

  private def formatDate(date: String): String = {
    // Handle different date formats and convert to YYYY-MM-DD
    // If only year is available, use YYYY-00-00
    // If year and month, use YYYY-MM-00
    val datePattern = """(\d{4})(?:-(\d{2}))?(?:-(\d{2}))?.*""".r

    date match {
      case datePattern(year, null, null) => s"$year-00-00"
      case datePattern(year, month, null) => s"$year-$month-00"
      case datePattern(year, month, day) => s"$year-$month-$day"
      case _ => date // Return as is if format is unknown
    }
  }

  // Create metadata file required by DataExtractorService
  private def createMetadataFile(watchlistSource: WatchlistSource, recordCount: Int): Unit = {
    val metadataFile = new File(s"${outputDir}/${watchlistSource.webScraperScriptName}_meta_data.txt")
    val writer = new BufferedWriter(new FileWriter(metadataFile))
    try {
      // Write the record count to the metadata file
      // If recordCount is 0, write 0 to indicate no updates
      // If recordCount > 0, write the count to indicate successful extraction with records
      writer.write(recordCount.toString)
    } finally {
      writer.close()
    }
  }

  // Read an entity from a file by ID as a string
  private def readEntityAsString(filePath: String, entityId: String): String = {
    // Get the entity location from the map
    val entityLocation = entityLocationMap.get(entityId).getOrElse(
      throw new RuntimeException(s"Entity $entityId not found in location map"))

    val startTime = System.currentTimeMillis()
    val randomAccessFile = new RandomAccessFile(filePath, "r")

    try {
      // Jump directly to the entity's position in the file
      randomAccessFile.seek(entityLocation.filePosition)

      // Read the line at that position
      val line = randomAccessFile.readLine()

      if (line == null) {
        throw new RuntimeException(s"Null line read for entity $entityId at position ${entityLocation.filePosition} in file $filePath")
      }

      // Convert bytes to UTF-8 string and sanitize
      val utf8Line = new String(line.getBytes("ISO-8859-1"), StandardCharsets.UTF_8)
      val sanitizedLine = utf8Line.replaceAll("[\\p{Cntrl}&&[^\r\n\t]]", "")

      val endTime = System.currentTimeMillis()
      val timeMs = endTime - startTime

      // Only log if it took more than 10ms to find the entity
      if (timeMs > 10 && processedTargetCount % 1000 == 0) { // Reduce log volume
        logger.info(s"ENTITY LOAD: Found entity $entityId in ${timeMs}ms using direct file access from $filePath")
      }

      sanitizedLine
    } catch {
      case e: Exception =>
        val endTime = System.currentTimeMillis()
        logger.warn(s"ENTITY LOAD: Failed to load entity $entityId in ${endTime - startTime}ms from $filePath: ${e.getMessage}")
        throw e
    } finally {
      randomAccessFile.close()
    }
  }

  // Create schema-specific index for faster lookups
  private def updateSchemaIndex(schema: String, id: String, position: Long): Unit = {
    val schemaIndex = schemaIndices.getOrElseUpdate(schema, MutableMap[String, Long]())
    schemaIndex(id) = position
  }

  // Get entity location using schema-specific index for faster lookups - optimized version
  private def getEntityLocation(id: String): EntityLocation = {
    // Get the entity location to determine the schema
    val location = entityLocationMap.getOrElse(id, null)
    if (location == null) {
      throw new RuntimeException(s"Entity $id not found in location map")
    }

    val schema = location.schema

    // Direct index lookup without option handling for common case
    val schemaIndex = schemaIndices.getOrElse(schema, null)
    if (schemaIndex != null) {
      val position = schemaIndex.getOrElse(id, -1L)
      if (position != -1L) {
        // Use the file path from the original location
        return EntityLocation(schema, location.filePath, 0, position)
      }
    }

    // If we didn't find a position in the index, just return the original location
    location
  }

  // Filter out unnecessary fields from entity JSON to reduce storage and processing time
  private def filterEntityProperties(entityJson: String): String = {
    import org.json4s.JsonDSL._
    import org.json4s.jackson.JsonMethods._

    try {
      // Parse the entity
      val entity = parse(entityJson)

      // Extract only the fields we need using direct field access
      // This is more robust than extracting to a map and filtering
      val id = (entity \ "id")
      val schema = (entity \ "schema")
      val properties = (entity \ "properties")
      val caption = (entity \ "caption")
      val target = (entity \ "target")

      // Build a new JSON object with only the fields we want to keep
      // Note: We've removed last_change as it's not needed for processing
      val filteredEntity =
        ("id" -> id) ~
        ("schema" -> schema) ~
        ("properties" -> properties) ~
        ("caption" -> caption) ~
        ("target" -> target)

      // Convert to compact JSON string
      compact(render(filteredEntity))
    } catch {
      case e: Exception =>
        logger.warn(s"Error filtering entity properties: ${e.getMessage}. Using original JSON.")
        entityJson
    }
  }
}
